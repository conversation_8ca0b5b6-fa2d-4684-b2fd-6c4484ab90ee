#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据一致性管理器
提供数据完整性检查、自动备份、事务性操作、数据恢复等功能
"""

import os
import json
import time
import hashlib
import threading
import shutil
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager

from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from src.utils.logger import logger
from src.utils.enhanced_error_handler import enhanced_error_handler, ErrorDomain

class DataIntegrityLevel(Enum):
    """数据完整性级别"""
    BASIC = "basic"           # 基础检查
    STANDARD = "standard"     # 标准检查
    STRICT = "strict"         # 严格检查
    PARANOID = "paranoid"     # 偏执检查

class BackupType(Enum):
    """备份类型"""
    MANUAL = "manual"         # 手动备份
    AUTO = "auto"            # 自动备份
    TRANSACTION = "transaction"  # 事务备份
    CHECKPOINT = "checkpoint"    # 检查点备份

@dataclass
class DataChecksum:
    """数据校验和"""
    file_path: str
    checksum: str
    size: int
    modified_time: float
    created_time: datetime = field(default_factory=datetime.now)

@dataclass
class BackupRecord:
    """备份记录"""
    backup_id: str
    backup_type: BackupType
    source_path: str
    backup_path: str
    checksum: str
    size: int
    created_time: datetime
    description: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IntegrityCheckResult:
    """完整性检查结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    corrupted_files: List[str] = field(default_factory=list)
    missing_files: List[str] = field(default_factory=list)
    checksum_mismatches: List[str] = field(default_factory=list)

class DataIntegrityManager(QObject):
    """数据完整性管理器"""
    
    # 信号
    integrity_check_completed = pyqtSignal(bool)  # is_valid
    backup_created = pyqtSignal(str)  # backup_id
    data_corruption_detected = pyqtSignal(str)  # file_path
    auto_recovery_completed = pyqtSignal(bool)  # success
    
    def __init__(self, project_root: str = None):
        super().__init__()
        
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.integrity_dir = self.project_root / ".integrity"
        self.integrity_dir.mkdir(exist_ok=True)
        
        # 配置文件
        self.config_file = self.integrity_dir / "integrity_config.json"
        self.checksums_file = self.integrity_dir / "checksums.json"
        self.backups_file = self.integrity_dir / "backups.json"
        
        # 数据存储
        self.checksums: Dict[str, DataChecksum] = {}
        self.backup_records: Dict[str, BackupRecord] = {}
        self.config = self._load_config()
        
        # 锁机制
        self._lock = threading.RLock()
        self._transaction_lock = threading.Lock()
        
        # 自动备份定时器
        self.auto_backup_timer = QTimer()
        self.auto_backup_timer.timeout.connect(self._perform_auto_backup)
        
        # 完整性检查定时器
        self.integrity_check_timer = QTimer()
        self.integrity_check_timer.timeout.connect(self._perform_integrity_check)
        
        # 加载现有数据
        self._load_checksums()
        self._load_backup_records()
        
        # 启动定时器
        self._start_timers()
        
        logger.info(f"数据完整性管理器初始化完成: {self.project_root}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            "integrity_level": DataIntegrityLevel.STANDARD.value,
            "auto_backup_enabled": True,
            "auto_backup_interval": 300,  # 5分钟
            "integrity_check_interval": 600,  # 10分钟
            "max_backups": 10,
            "backup_compression": True,
            "watched_files": [
                "project.json",
                "config/*.json",
                "src/**/*.py"
            ],
            "critical_files": [
                "project.json"
            ]
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"加载完整性配置失败: {e}")
            return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存完整性配置失败: {e}")
    
    def _load_checksums(self):
        """加载校验和数据"""
        try:
            if self.checksums_file.exists():
                with open(self.checksums_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for file_path, checksum_data in data.items():
                    self.checksums[file_path] = DataChecksum(
                        file_path=checksum_data['file_path'],
                        checksum=checksum_data['checksum'],
                        size=checksum_data['size'],
                        modified_time=checksum_data['modified_time'],
                        created_time=datetime.fromisoformat(checksum_data['created_time'])
                    )
        except Exception as e:
            logger.error(f"加载校验和数据失败: {e}")
    
    def _save_checksums(self):
        """保存校验和数据"""
        try:
            data = {}
            for file_path, checksum in self.checksums.items():
                data[file_path] = {
                    'file_path': checksum.file_path,
                    'checksum': checksum.checksum,
                    'size': checksum.size,
                    'modified_time': checksum.modified_time,
                    'created_time': checksum.created_time.isoformat()
                }
            
            with open(self.checksums_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存校验和数据失败: {e}")
    
    def _load_backup_records(self):
        """加载备份记录"""
        try:
            if self.backups_file.exists():
                with open(self.backups_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for backup_id, record_data in data.items():
                    self.backup_records[backup_id] = BackupRecord(
                        backup_id=record_data['backup_id'],
                        backup_type=BackupType(record_data['backup_type']),
                        source_path=record_data['source_path'],
                        backup_path=record_data['backup_path'],
                        checksum=record_data['checksum'],
                        size=record_data['size'],
                        created_time=datetime.fromisoformat(record_data['created_time']),
                        description=record_data.get('description', ''),
                        metadata=record_data.get('metadata', {})
                    )
        except Exception as e:
            logger.error(f"加载备份记录失败: {e}")
    
    def _save_backup_records(self):
        """保存备份记录"""
        try:
            data = {}
            for backup_id, record in self.backup_records.items():
                data[backup_id] = {
                    'backup_id': record.backup_id,
                    'backup_type': record.backup_type.value,
                    'source_path': record.source_path,
                    'backup_path': record.backup_path,
                    'checksum': record.checksum,
                    'size': record.size,
                    'created_time': record.created_time.isoformat(),
                    'description': record.description,
                    'metadata': record.metadata
                }
            
            with open(self.backups_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存备份记录失败: {e}")
    
    def _start_timers(self):
        """启动定时器"""
        if self.config.get('auto_backup_enabled', True):
            interval = self.config.get('auto_backup_interval', 300) * 1000  # 转换为毫秒
            self.auto_backup_timer.start(interval)
            logger.info(f"自动备份定时器已启动，间隔: {interval/1000}秒")
        
        integrity_interval = self.config.get('integrity_check_interval', 600) * 1000
        self.integrity_check_timer.start(integrity_interval)
        logger.info(f"完整性检查定时器已启动，间隔: {integrity_interval/1000}秒")
    
    def calculate_file_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件校验和失败 {file_path}: {e}")
            return ""
    
    def update_file_checksum(self, file_path: str) -> bool:
        """更新文件校验和"""
        try:
            with self._lock:
                abs_path = str(Path(file_path).resolve())
                
                if not os.path.exists(abs_path):
                    logger.warning(f"文件不存在，无法更新校验和: {abs_path}")
                    return False
                
                checksum = self.calculate_file_checksum(abs_path)
                if not checksum:
                    return False
                
                stat = os.stat(abs_path)
                
                self.checksums[abs_path] = DataChecksum(
                    file_path=abs_path,
                    checksum=checksum,
                    size=stat.st_size,
                    modified_time=stat.st_mtime
                )
                
                self._save_checksums()
                return True
                
        except Exception as e:
            logger.error(f"更新文件校验和失败 {file_path}: {e}")
            return False
    
    def verify_file_integrity(self, file_path: str) -> bool:
        """验证文件完整性"""
        try:
            abs_path = str(Path(file_path).resolve())
            
            if abs_path not in self.checksums:
                logger.warning(f"文件没有校验和记录: {abs_path}")
                return True  # 新文件认为是有效的
            
            if not os.path.exists(abs_path):
                logger.error(f"文件缺失: {abs_path}")
                return False
            
            stored_checksum = self.checksums[abs_path]
            current_checksum = self.calculate_file_checksum(abs_path)
            
            if current_checksum != stored_checksum.checksum:
                logger.error(f"文件校验和不匹配: {abs_path}")
                self.data_corruption_detected.emit(abs_path)
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证文件完整性失败 {file_path}: {e}")
            return False
