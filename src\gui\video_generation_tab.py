# -*- coding: utf-8 -*-
"""
视频生成标签页
用于将配音、图像等数据传递到视频生成界面，进行视频生成操作
"""

import os
import json
import asyncio
from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QComboBox, QFormLayout, QGroupBox, QMessageBox,
    QProgressBar, QTextEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QFrame,
    QSplitter, QHeaderView, QAbstractItemView, QSlider
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap

from src.utils.logger import logger
from src.utils.project_manager import StoryboardProjectManager


class VideoGenerationWorker(QThread):
    """视频生成工作线程"""
    
    progress_updated = pyqtSignal(int, str)  # 进度, 消息
    video_generated = pyqtSignal(str, bool, str)  # 视频路径, 成功状态, 错误信息
    
    def __init__(self, scene_data, generation_config, project_manager, project_name):
        super().__init__()
        self.scene_data = scene_data
        self.generation_config = generation_config
        self.project_manager = project_manager
        self.project_name = project_name
        self.is_cancelled = False
    
    def run(self):
        """运行视频生成"""
        try:
            # 创建异步事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 运行异步生成
            result = loop.run_until_complete(self._generate_video_async())
            
            if result and result.success:
                self.video_generated.emit(result.video_path, True, "")
            else:
                error_msg = result.error_message if result else "未知错误"
                self.video_generated.emit("", False, error_msg)
                
        except Exception as e:
            logger.error(f"视频生成线程异常: {e}")
            self.video_generated.emit("", False, str(e))
        finally:
            loop.close()
    
    async def _generate_video_async(self):
        """异步生成视频"""
        # 🔧 将Result类定义移到方法开始，避免作用域问题
        class Result:
            def __init__(self, success, video_path="", error_message=""):
                self.success = success
                self.video_path = video_path
                self.error_message = error_message

        try:
            from src.processors.video_processor import VideoProcessor
            from src.core.service_manager import ServiceManager

            # 创建视频处理器
            service_manager = ServiceManager()
            processor = VideoProcessor(service_manager)

            # 更新进度
            self.progress_updated.emit(10, "准备视频生成...")

            # 从场景数据生成视频
            image_path = self.scene_data.get('image_path', '')
            # 🔧 优先使用video_prompt字段（来自prompt.json的content）
            prompt = self.scene_data.get('video_prompt',
                     self.scene_data.get('enhanced_description',
                     self.scene_data.get('description', '')))

            # 🔧 检查是否启用AI音效，如果启用则添加音效提示到prompt中
            audio_hint = self._get_audio_hint_for_scene(self.scene_data)
            if audio_hint and self._is_audio_enabled_for_scene(self.scene_data):
                prompt = f"{prompt}。音效：{audio_hint}"

            if not image_path or not os.path.exists(image_path):
                raise Exception(f"图像文件不存在: {image_path}")

            self.progress_updated.emit(30, "开始生成视频...")

            # 生成视频
            video_path = await processor.generate_video_from_image(
                image_path=image_path,
                prompt=prompt,
                duration=self.generation_config.get('duration', 5.0),
                fps=self.generation_config.get('fps', 24),
                width=self.generation_config.get('width', 1024),
                height=self.generation_config.get('height', 1024),
                motion_intensity=self.generation_config.get('motion_intensity', 0.5),
                preferred_engine=self.generation_config.get('engine', 'cogvideox_flash'),
                progress_callback=lambda p, msg: self.progress_updated.emit(30 + int(p * 60), msg),
                project_manager=self.project_manager,
                current_project_name=self.project_name
            )

            self.progress_updated.emit(100, "视频生成完成!")
            return Result(True, video_path)

        except Exception as e:
            logger.error(f"异步视频生成失败: {e}")
            return Result(False, "", str(e))
    
    def cancel(self):
        """取消生成"""
        self.is_cancelled = True


class VideoGenerationTab(QWidget):
    """视频生成标签页"""
    
    def __init__(self, app_controller, project_manager: StoryboardProjectManager, parent=None):
        super().__init__(parent)
        self.app_controller = app_controller
        self.project_manager = project_manager
        self.parent_window = parent
        
        # 当前数据
        self.current_scenes = []
        self.current_voices = []
        self.generation_queue = []
        self.current_worker = None
        
        self.init_ui()
        self.load_project_data()
    
    def init_ui(self):
        """初始化UI界面"""
        main_layout = QVBoxLayout()

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("🎬 视频生成")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.clicked.connect(self.load_project_data)
        refresh_btn.setToolTip("重新加载项目中的配音和图像数据")
        title_layout.addWidget(refresh_btn)

        main_layout.addLayout(title_layout)

        # 创建主水平分割器 - 左侧场景列表，右侧预览面板
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：场景列表面板
        scene_list_panel = self.create_scene_list_panel()
        main_splitter.addWidget(scene_list_panel)

        # 右侧：当前选择预览面板
        preview_panel = self.create_current_selection_preview()
        main_splitter.addWidget(preview_panel)

        # 设置分割器比例 - 左侧场景列表占大部分，右侧预览占小部分
        main_splitter.setSizes([800, 300])
        main_splitter.setCollapsible(0, False)
        main_splitter.setCollapsible(1, False)

        main_layout.addWidget(main_splitter)

        # 底部：进度条和状态
        self.create_progress_area(main_layout)

        self.setLayout(main_layout)
    
    def create_scene_list_panel(self):
        """创建场景列表面板（左侧红框区域）"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setStyleSheet("QFrame { border: 1px solid #ddd; }")  # 淡色边框
        layout = QVBoxLayout(panel)

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("📋 场景列表")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # 添加场景统计信息
        self.scene_count_label = QLabel("共 0 个场景")
        self.scene_count_label.setStyleSheet("color: #666; font-size: 12px;")
        title_layout.addWidget(self.scene_count_label)

        layout.addLayout(title_layout)

        # 场景表格 - 扩大显示区域
        self.scene_table = QTableWidget()
        self.scene_table.setColumnCount(8)
        self.scene_table.setHorizontalHeaderLabels([
            "选择", "镜头", "旁白", "配音", "图像", "AI音效", "视频", "状态"
        ])

        # 设置表格属性
        self.scene_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.scene_table.setAlternatingRowColors(True)
        self.scene_table.setMinimumHeight(400)  # 增加最小高度

        # 设置表格可调整大小
        header = self.scene_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(False)

        # 设置垂直表头可调整行高
        v_header = self.scene_table.verticalHeader()
        v_header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        v_header.setVisible(True)
        v_header.setDefaultSectionSize(120)  # 增加默认行高以容纳更大的缩略图

        # 设置初始列宽 - 针对更宽的左侧面板优化
        self.scene_table.setColumnWidth(0, 60)   # 选择
        self.scene_table.setColumnWidth(1, 140)  # 镜头
        self.scene_table.setColumnWidth(2, 200)  # 旁白 - 增加宽度
        self.scene_table.setColumnWidth(3, 100)  # 配音
        self.scene_table.setColumnWidth(4, 220)  # 图像 - 进一步增加宽度以显示更大缩略图
        self.scene_table.setColumnWidth(5, 180)  # AI音效 - 增加宽度
        self.scene_table.setColumnWidth(6, 140)  # 视频
        self.scene_table.setColumnWidth(7, 120)  # 状态
        
        layout.addWidget(self.scene_table)
        
        # 底部按钮布局
        bottom_layout = QHBoxLayout()

        # 左侧按钮组
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_scenes)
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        bottom_layout.addWidget(self.select_all_btn)

        self.select_none_btn = QPushButton("取消全选")
        self.select_none_btn.clicked.connect(self.select_none_scenes)
        self.select_none_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        bottom_layout.addWidget(self.select_none_btn)

        bottom_layout.addStretch()

        # 右侧批量生成按钮
        self.batch_generate_btn = QPushButton("批量生成")
        self.batch_generate_btn.clicked.connect(self.start_batch_generation)
        self.batch_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 4px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        bottom_layout.addWidget(self.batch_generate_btn)

        layout.addLayout(bottom_layout)
        
        return panel

    def create_current_selection_preview(self):
        """创建当前选择预览面板（右侧）"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setStyleSheet("QFrame { border: 1px solid #ddd; }")
        panel.setFixedWidth(320)  # 固定宽度，与截图一致
        layout = QVBoxLayout(panel)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel("🎯 当前选择预览")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 图像预览区域 - 大图显示
        self.image_preview = QLabel("选择场景查看图像预览")
        self.image_preview.setMinimumHeight(280)  # 稍微减少高度，为控制按钮留出空间
        self.image_preview.setMaximumHeight(280)
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setStyleSheet("""
            QLabel {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
                text-align: center;
                font-size: 14px;
                color: #7f8c8d;
            }
        """)
        # 不使用setScaledContents，改为手动缩放以避免覆盖问题
        layout.addWidget(self.image_preview)

        # 图像切换控制 - 放大按钮，移到原场景描述位置
        image_control_widget = QWidget()
        image_control_widget.setFixedHeight(80)  # 增加高度，替代场景描述区域
        image_control_layout = QHBoxLayout(image_control_widget)
        image_control_layout.setContentsMargins(10, 15, 10, 15)

        self.prev_image_btn = QPushButton("◀ 上一张")
        self.prev_image_btn.setFixedSize(100, 50)  # 放大按钮
        self.prev_image_btn.setVisible(False)
        self.prev_image_btn.clicked.connect(self.show_previous_image)
        self.prev_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        self.image_info_label = QLabel("")
        self.image_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_info_label.setStyleSheet("color: #2c3e50; font-size: 16px; font-weight: bold;")

        self.next_image_btn = QPushButton("下一张 ▶")
        self.next_image_btn.setFixedSize(100, 50)  # 放大按钮
        self.next_image_btn.setVisible(False)
        self.next_image_btn.clicked.connect(self.show_next_image)
        self.next_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        image_control_layout.addWidget(self.prev_image_btn)
        image_control_layout.addStretch()
        image_control_layout.addWidget(self.image_info_label)
        image_control_layout.addStretch()
        image_control_layout.addWidget(self.next_image_btn)

        layout.addWidget(image_control_widget)

        # 生成设置区域
        settings_label = QLabel("生成设置:")
        settings_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        settings_label.setStyleSheet("color: #2c3e50; margin-top: 15px;")
        layout.addWidget(settings_label)

        # 设置表单
        settings_form = QFormLayout()
        settings_form.setSpacing(8)

        # 动态时长显示标签
        self.dynamic_duration_label = QLabel("选择镜头查看配音时长")
        self.dynamic_duration_label.setStyleSheet("color: #666; font-style: italic; font-size: 12px;")
        settings_form.addRow("视频时长:", self.dynamic_duration_label)

        # 多片段提示标签
        self.multi_segment_label = QLabel("")
        self.multi_segment_label.setStyleSheet("color: #FF9800; font-size: 11px; font-weight: bold;")
        self.multi_segment_label.setVisible(False)
        settings_form.addRow("", self.multi_segment_label)

        # 分辨率 - 使用CogVideoX-Flash支持的分辨率
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "1024x1024 (推荐)",
            "720x480",
            "1280x960",
            "960x1280",
            "1920x1080",
            "1080x1920",
            "2048x1080",
            "3840x2160 (4K)"
        ])
        self.resolution_combo.setStyleSheet("""
            QComboBox {
                padding: 4px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
        """)
        settings_form.addRow("分辨率:", self.resolution_combo)

        # 帧率 - 使用CogVideoX-Flash支持的帧率
        self.fps_combo = QComboBox()
        self.fps_combo.addItems(["30", "60"])
        self.fps_combo.setCurrentText("30")
        self.fps_combo.setStyleSheet("""
            QComboBox {
                padding: 4px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
        """)
        settings_form.addRow("帧率:", self.fps_combo)

        # 运动强度
        motion_layout = QHBoxLayout()
        self.motion_slider = QSlider(Qt.Orientation.Horizontal)
        self.motion_slider.setRange(0, 100)
        self.motion_slider.setValue(50)
        self.motion_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #bdc3c7;
                height: 6px;
                background: #ecf0f1;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #3498db;
                border: 1px solid #2980b9;
                width: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }
        """)
        self.motion_label = QLabel("50%")
        self.motion_label.setFixedWidth(40)
        self.motion_label.setStyleSheet("font-size: 12px; color: #2c3e50;")
        self.motion_slider.valueChanged.connect(
            lambda v: self.motion_label.setText(f"{v}%")
        )
        motion_layout.addWidget(self.motion_slider)
        motion_layout.addWidget(self.motion_label)
        settings_form.addRow("运动强度:", motion_layout)

        # 自动播放
        self.auto_play_check = QCheckBox("生成完成后自动播放")
        self.auto_play_check.setChecked(True)
        self.auto_play_check.setStyleSheet("font-size: 12px; color: #2c3e50;")
        settings_form.addRow("", self.auto_play_check)

        layout.addLayout(settings_form)

        # 生成当前选择按钮
        self.single_generate_btn = QPushButton("🎥 生成当前选择")
        self.single_generate_btn.clicked.connect(self.start_single_generation)
        self.single_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 12px 20px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
                margin-top: 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        layout.addWidget(self.single_generate_btn)

        layout.addStretch()
        return panel



    def start_batch_generation(self):
        """开始批量生成所有视频"""
        if not self.current_scenes:
            QMessageBox.warning(self, "警告", "没有可生成的场景数据")
            return

        # 这里可以添加批量生成的逻辑
        QMessageBox.information(self, "提示", f"将批量生成 {len(self.current_scenes)} 个视频")

    def create_progress_area(self, parent_layout):
        """创建进度区域"""
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        progress_layout = QVBoxLayout(progress_frame)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666;")
        progress_layout.addWidget(self.status_label)

        # 控制按钮
        control_layout = QHBoxLayout()

        self.cancel_btn = QPushButton("❌ 取消生成")
        self.cancel_btn.clicked.connect(self.cancel_generation)
        self.cancel_btn.setVisible(False)
        control_layout.addWidget(self.cancel_btn)

        control_layout.addStretch()

        progress_layout.addLayout(control_layout)
        parent_layout.addWidget(progress_frame)

    def load_project_data(self):
        """加载项目数据"""
        try:
            if not self.project_manager or not self.project_manager.current_project:
                self.status_label.setText("未加载项目")
                return

            project_data = self.project_manager.get_project_data()
            if not project_data:
                self.status_label.setText("项目数据为空")
                return

            # 加载场景数据
            self.load_scenes_data(project_data)

            # 更新状态
            scene_count = len(self.current_scenes)
            self.status_label.setText(f"已加载 {scene_count} 个场景")

        except Exception as e:
            logger.error(f"加载项目数据失败: {e}")
            self.status_label.setText(f"加载失败: {e}")

    def load_scenes_data(self, project_data):
        """加载场景数据"""
        try:
            self.current_scenes = []
            self.project_data = project_data  # 保存project_data以便在其他方法中使用
            logger.info(f"开始加载场景数据，项目数据键: {list(project_data.keys())}")

            # 尝试多种数据源加载场景数据
            scenes_loaded = False

            # 方法1：从新的项目数据结构中提取
            if not scenes_loaded:
                scenes_loaded = self._load_from_new_structure(project_data)

            # 方法2：从旧的项目数据结构中提取
            if not scenes_loaded:
                scenes_loaded = self._load_from_legacy_structure(project_data)

            # 方法3：从分镜图像生成数据中提取
            if not scenes_loaded:
                scenes_loaded = self._load_from_storyboard_data(project_data)

            if not scenes_loaded:
                logger.warning("未能从任何数据源加载场景数据")
                self.status_label.setText("未找到场景数据")
                return

            # 更新表格显示
            self.update_scene_table()
            logger.info(f"成功加载 {len(self.current_scenes)} 个场景")

        except Exception as e:
            logger.error(f"加载场景数据失败: {e}")
            raise

    def _load_from_new_structure(self, project_data):
        """从新的项目数据结构加载"""
        try:
            # 方法1：从voice_generation.voice_segments加载（优先）
            voice_generation = project_data.get('voice_generation', {})
            voice_segments = voice_generation.get('voice_segments', [])
            if voice_segments:
                logger.info(f"从voice_generation.voice_segments加载，找到 {len(voice_segments)} 个镜头")
                return self._load_from_voice_segments(voice_segments, project_data)

            # 方法2：从shots_data加载
            shots_data = project_data.get('shots_data', [])
            if shots_data:
                logger.info(f"从shots_data加载，找到 {len(shots_data)} 个镜头")
                return self._load_from_shots_data(shots_data, project_data)

            # 方法3：从storyboard.shots加载
            storyboard = project_data.get('storyboard', {})
            shots = storyboard.get('shots', [])
            if shots:
                logger.info(f"从storyboard.shots加载，找到 {len(shots)} 个镜头")
                return self._load_from_storyboard_shots(shots, project_data)

            # 方法4：从五阶段数据加载（最后选择）
            five_stage_data = project_data.get('five_stage_storyboard', {})
            if five_stage_data:
                logger.info("尝试从五阶段数据加载")
                return self._load_from_five_stage_data(five_stage_data, project_data)

            return False

        except Exception as e:
            logger.error(f"从新结构加载失败: {e}")
            return False

    def _load_from_voice_segments(self, voice_segments, project_data):
        """从voice_generation.voice_segments加载"""
        try:
            self.current_scenes = []

            # 获取enhanced_descriptions和image_generation数据
            enhanced_descriptions = project_data.get('enhanced_descriptions', {})
            image_generation = project_data.get('image_generation', {})

            # 🔧 新增：加载prompt.json文件中的content字段
            prompt_data = self._load_prompt_json()

            # 使用全局镜头编号，与分镜图像生成界面保持一致
            global_shot_counter = 1

            for segment in voice_segments:
                scene_data = self._create_scene_data_from_voice_segment(
                    segment, enhanced_descriptions, image_generation, project_data, global_shot_counter, prompt_data
                )
                if scene_data:
                    self.current_scenes.append(scene_data)
                    global_shot_counter += 1

            logger.info(f"从voice_segments加载了 {len(self.current_scenes)} 个镜头")
            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从voice_segments加载失败: {e}")
            return False

    def _load_prompt_json(self):
        """加载prompt.json文件中的content字段"""
        try:
            if not self.project_manager or not self.project_manager.current_project:
                return {}

            project_dir = self.project_manager.current_project.get('project_dir', '')
            if not project_dir:
                return {}

            prompt_file = os.path.join(project_dir, 'texts', 'prompt.json')
            if not os.path.exists(prompt_file):
                logger.warning(f"prompt.json文件不存在: {prompt_file}")
                return {}

            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt_data = json.load(f)

            # 提取所有镜头的content字段
            content_map = {}
            scenes = prompt_data.get('scenes', {})
            for scene_name, shots in scenes.items():
                for i, shot in enumerate(shots):
                    shot_number = shot.get('shot_number', f'### 镜头{i+1}')
                    content = shot.get('content', '')
                    if content:
                        # 使用镜头编号作为键
                        shot_key = shot_number.replace('### ', '')
                        content_map[shot_key] = content

            logger.info(f"从prompt.json加载了 {len(content_map)} 个镜头的content字段")
            return content_map

        except Exception as e:
            logger.error(f"加载prompt.json失败: {e}")
            return {}

    def _create_scene_data_from_voice_segment(self, segment, enhanced_descriptions, image_generation, project_data, global_shot_number, prompt_data=None):
        """从voice_segment创建场景数据"""
        try:
            shot_id = segment.get('shot_id', '')
            scene_id = segment.get('scene_id', '')

            # 使用全局镜头编号，与分镜图像生成界面保持一致
            global_shot_id = f"镜头{global_shot_number}"

            # 创建基础场景数据
            scene_data = {
                'shot_id': global_shot_id,  # 使用全局镜头编号
                'scene_id': scene_id,
                'shot_number': global_shot_number,
                'shot_title': global_shot_id,  # 使用全局镜头编号
                'scene_title': scene_id,
                'narration': segment.get('original_text', ''),
                'original_description': segment.get('original_text', ''),
                'description': segment.get('storyboard_description', ''),
                'enhanced_description': '',
                'voice_path': segment.get('audio_path', ''),
                'voice_duration': 0.0,
                'image_path': '',
                'video_path': '',
                'status': '未生成'
            }

            # 获取配音时长
            if scene_data['voice_path'] and os.path.exists(scene_data['voice_path']):
                voice_duration = self._get_audio_duration(scene_data['voice_path'])
                scene_data['voice_duration'] = voice_duration
                logger.info(f"获取音频时长: {scene_data['voice_path']} -> {voice_duration:.1f}s")

            # 🔧 优先从prompt.json获取content字段作为视频生成描述
            if prompt_data and global_shot_id in prompt_data:
                scene_data['enhanced_description'] = prompt_data[global_shot_id]
                scene_data['video_prompt'] = prompt_data[global_shot_id]
                logger.debug(f"从prompt.json获取{global_shot_id}的content: {prompt_data[global_shot_id][:50]}...")
            else:
                # 从enhanced_descriptions获取图像信息（备用）
                shot_key = f"### {shot_id}"
                if shot_key in enhanced_descriptions:
                    enhanced_data = enhanced_descriptions[shot_key]
                    scene_data['enhanced_prompt'] = enhanced_data.get('enhanced_prompt', '')
                    scene_data['enhanced_description'] = enhanced_data.get('enhanced_prompt', '')
                    scene_data['video_prompt'] = enhanced_data.get('enhanced_prompt', '')

            # 从image_generation获取图像路径
            if shot_id in image_generation:
                image_data = image_generation[shot_id]
                scene_data['image_path'] = image_data.get('image_path', '')
                scene_data['status'] = image_data.get('status', '未生成')

            return scene_data

        except Exception as e:
            logger.error(f"创建场景数据失败: {e}")
            return None

    def _load_from_shots_data(self, shots_data, project_data):
        """从shots_data加载"""
        try:
            # 获取配音数据
            voice_generation = project_data.get('voice_generation', {})
            voice_segments = voice_generation.get('segments', [])

            # 获取图像数据
            image_generation = project_data.get('image_generation', {})
            images = image_generation.get('images', [])

            # 获取视频数据
            video_generation = project_data.get('video_generation', {})
            videos = video_generation.get('videos', [])

            # 处理每个镜头
            for i, shot in enumerate(shots_data):
                shot_id = shot.get('shot_id', f'shot_{i+1}')
                scene_id = shot.get('scene_id', f'scene_{i//5+1}')  # 假设每5个镜头一个场景

                scene_data = self._create_scene_data(shot_id, scene_id, shot, voice_segments, images, videos)
                self.current_scenes.append(scene_data)

            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从shots_data加载失败: {e}")
            return False

    def _load_from_storyboard_shots(self, shots, project_data):
        """从storyboard.shots加载"""
        try:
            # 获取配音数据
            voice_generation = project_data.get('voice_generation', {})
            voice_segments = voice_generation.get('segments', [])

            # 获取图像数据
            image_generation = project_data.get('image_generation', {})
            images = image_generation.get('images', [])

            # 获取视频数据
            video_generation = project_data.get('video_generation', {})
            videos = video_generation.get('videos', [])

            # 处理每个镜头
            for shot in shots:
                shot_id = shot.get('shot_id', '')
                scene_id = shot.get('scene_id', '')

                scene_data = self._create_scene_data(shot_id, scene_id, shot, voice_segments, images, videos)
                self.current_scenes.append(scene_data)

            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从storyboard.shots加载失败: {e}")
            return False

    def _load_from_five_stage_data(self, five_stage_data, project_data):
        """从五阶段数据加载"""
        try:
            stage_data = five_stage_data.get('stage_data', {})

            # 从第5阶段获取最终分镜数据
            stage_5 = stage_data.get('5', {})
            final_storyboard = stage_5.get('final_storyboard', [])

            if not final_storyboard:
                # 尝试从第4阶段获取
                stage_4 = stage_data.get('4', {})
                storyboard_results = stage_4.get('storyboard_results', [])
                if storyboard_results:
                    # 展开所有场景的镜头
                    final_storyboard = []
                    for scene_result in storyboard_results:
                        voice_segments = scene_result.get('voice_segments', [])
                        final_storyboard.extend(voice_segments)

            if not final_storyboard:
                return False

            logger.info(f"从五阶段数据加载，找到 {len(final_storyboard)} 个镜头")

            # 获取配音数据
            voice_generation = project_data.get('voice_generation', {})
            voice_segments = voice_generation.get('segments', [])

            # 获取图像数据
            image_generation = project_data.get('image_generation', {})
            images = image_generation.get('images', [])

            # 获取视频数据
            video_generation = project_data.get('video_generation', {})
            videos = video_generation.get('videos', [])

            # 处理每个镜头
            for i, shot in enumerate(final_storyboard):
                shot_id = shot.get('shot_id', f'shot_{i+1}')
                scene_id = shot.get('scene_id', f'scene_{i//5+1}')

                scene_data = self._create_scene_data(shot_id, scene_id, shot, voice_segments, images, videos)
                self.current_scenes.append(scene_data)

            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从五阶段数据加载失败: {e}")
            return False

    def _load_from_legacy_structure(self, project_data):
        """从旧的项目数据结构加载"""
        try:
            # 方法1：从shot_image_mappings加载
            shot_image_mappings = project_data.get('shot_image_mappings', {})
            if shot_image_mappings:
                logger.info(f"从shot_image_mappings加载，找到 {len(shot_image_mappings)} 个镜头映射")
                return self._load_from_shot_mappings(shot_image_mappings, project_data)

            # 方法2：从旧的voices/images/videos结构加载
            voices = project_data.get('voices', {})
            images = project_data.get('images', {})
            videos = project_data.get('videos', {})
            scenes = project_data.get('scenes', [])

            if not scenes and not voices and not images:
                return False

            # 如果有voices/images数据但没有scenes，尝试从键名推断
            if (voices or images) and not scenes:
                return self._load_from_voice_image_keys(voices, images, videos, project_data)

            # 标准的scenes结构
            for scene_idx, scene in enumerate(scenes):
                shots = scene.get('shots', [])
                for shot_idx, shot in enumerate(shots):
                    shot_key = f"scene_{scene_idx}_shot_{shot_idx}"

                    scene_data = {
                        'scene_id': f"scene_{scene_idx}",
                        'shot_id': f"shot_{shot_idx}",
                        'scene_title': scene.get('title', f'场景{scene_idx + 1}'),
                        'shot_title': shot.get('title', f'镜头{shot_idx + 1}'),
                        'description': shot.get('description', ''),
                        'enhanced_description': shot.get('enhanced_description', ''),
                        'original_text': shot.get('original_text', ''),
                        'voice_path': '',
                        'voice_duration': 0.0,
                        'image_path': '',
                        'video_path': '',
                        'status': '未生成'
                    }

                    # 查找配音文件
                    if shot_key in voices:
                        voice_info = voices[shot_key]
                        if isinstance(voice_info, dict):
                            scene_data['voice_path'] = voice_info.get('file_path', '')
                            scene_data['voice_duration'] = voice_info.get('duration', 0.0)
                        else:
                            scene_data['voice_path'] = str(voice_info)

                    # 查找图像文件
                    if shot_key in images:
                        image_info = images[shot_key]
                        if isinstance(image_info, dict):
                            scene_data['image_path'] = image_info.get('file_path', '')
                        else:
                            scene_data['image_path'] = str(image_info)

                    # 查找视频文件
                    if shot_key in videos:
                        video_info = videos[shot_key]
                        if isinstance(video_info, dict):
                            scene_data['video_path'] = video_info.get('file_path', '')
                            if scene_data['video_path'] and os.path.exists(scene_data['video_path']):
                                scene_data['status'] = '已生成'

                    self.current_scenes.append(scene_data)

            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从旧结构加载失败: {e}")
            return False

    def _load_from_shot_mappings(self, shot_mappings, project_data):
        """从shot_image_mappings加载"""
        try:
            # 获取配音数据
            voice_generation = project_data.get('voice_generation', {})
            voice_segments = voice_generation.get('segments', [])

            # 获取视频数据
            video_generation = project_data.get('video_generation', {})
            videos = video_generation.get('videos', [])

            for shot_key, mapping_data in shot_mappings.items():
                # 解析shot_key (如: scene_1_shot_1)
                parts = shot_key.split('_')
                if len(parts) >= 4:
                    scene_id = f"{parts[0]}_{parts[1]}"
                    shot_id = f"{parts[2]}_{parts[3]}"
                else:
                    scene_id = f"scene_{len(self.current_scenes)//5+1}"
                    shot_id = f"shot_{len(self.current_scenes)+1}"

                scene_data = {
                    'scene_id': scene_id,
                    'shot_id': shot_id,
                    'scene_title': scene_id.replace('_', ' ').title(),
                    'shot_title': shot_id.replace('_', ' ').title(),
                    'description': mapping_data.get('enhanced_description', ''),
                    'enhanced_description': mapping_data.get('enhanced_description', ''),
                    'original_text': mapping_data.get('original_text', ''),
                    'voice_path': '',
                    'voice_duration': 0.0,
                    'image_path': mapping_data.get('main_image_path', ''),
                    'video_path': '',
                    'status': mapping_data.get('status', '未生成')
                }

                # 查找配音文件
                for voice_segment in voice_segments:
                    if (voice_segment.get('shot_id') == shot_id or
                        voice_segment.get('shot_id') == shot_key):
                        audio_path = voice_segment.get('audio_path', '')
                        if audio_path and os.path.exists(audio_path):
                            scene_data['voice_path'] = audio_path
                            scene_data['voice_duration'] = voice_segment.get('duration', 0.0)
                        break

                # 查找视频文件
                for video in videos:
                    if (video.get('shot_id') == shot_id or
                        video.get('shot_id') == shot_key):
                        video_path = video.get('video_path', '')
                        if video_path and os.path.exists(video_path):
                            scene_data['video_path'] = video_path
                            scene_data['status'] = '已生成'
                        break

                self.current_scenes.append(scene_data)

            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从shot_mappings加载失败: {e}")
            return False

    def _load_from_voice_image_keys(self, voices, images, videos, project_data):
        """从voice/image键名推断镜头数据"""
        try:
            # 收集所有的镜头键
            all_keys = set()
            all_keys.update(voices.keys())
            all_keys.update(images.keys())
            all_keys.update(videos.keys())

            if not all_keys:
                return False

            for shot_key in sorted(all_keys):
                # 解析shot_key
                parts = shot_key.split('_')
                if len(parts) >= 4:
                    scene_id = f"{parts[0]}_{parts[1]}"
                    shot_id = f"{parts[2]}_{parts[3]}"
                else:
                    scene_id = f"scene_{len(self.current_scenes)//5+1}"
                    shot_id = f"shot_{len(self.current_scenes)+1}"

                scene_data = {
                    'scene_id': scene_id,
                    'shot_id': shot_id,
                    'scene_title': scene_id.replace('_', ' ').title(),
                    'shot_title': shot_id.replace('_', ' ').title(),
                    'description': '',
                    'enhanced_description': '',
                    'original_text': '',
                    'voice_path': '',
                    'voice_duration': 0.0,
                    'image_path': '',
                    'video_path': '',
                    'status': '未生成'
                }

                # 处理配音数据
                if shot_key in voices:
                    voice_info = voices[shot_key]
                    if isinstance(voice_info, dict):
                        scene_data['voice_path'] = voice_info.get('file_path', '')
                        scene_data['voice_duration'] = voice_info.get('duration', 0.0)
                    else:
                        scene_data['voice_path'] = str(voice_info)

                # 处理图像数据
                if shot_key in images:
                    image_info = images[shot_key]
                    if isinstance(image_info, dict):
                        scene_data['image_path'] = image_info.get('file_path', '')
                    else:
                        scene_data['image_path'] = str(image_info)

                # 处理视频数据
                if shot_key in videos:
                    video_info = videos[shot_key]
                    if isinstance(video_info, dict):
                        scene_data['video_path'] = video_info.get('file_path', '')
                        if scene_data['video_path'] and os.path.exists(scene_data['video_path']):
                            scene_data['status'] = '已生成'

                self.current_scenes.append(scene_data)

            return len(self.current_scenes) > 0

        except Exception as e:
            logger.error(f"从voice/image键加载失败: {e}")
            return False

    def _load_from_storyboard_data(self, project_data):
        """从分镜数据加载"""
        try:
            # 尝试从分镜图像生成的数据中加载
            if hasattr(self.project_manager, 'get_storyboard_data'):
                storyboard_data = self.project_manager.get_storyboard_data()
                if storyboard_data:
                    for i, shot_data in enumerate(storyboard_data):
                        scene_data = {
                            'scene_id': shot_data.get('scene_name', f"scene_{i // 5 + 1}"),
                            'shot_id': shot_data.get('sequence', f"shot_{i + 1}"),
                            'scene_title': shot_data.get('scene_name', f"场景{i // 5 + 1}"),
                            'shot_title': shot_data.get('sequence', f"镜头{i + 1}"),
                            'description': shot_data.get('consistency_description', ''),
                            'enhanced_description': shot_data.get('enhanced_description', ''),
                            'original_description': shot_data.get('original_description', ''),  # 旁白内容
                            'original_text': shot_data.get('original_description', ''),  # 兼容字段
                            'narration': shot_data.get('original_description', ''),  # 兼容字段
                            'technical_details': shot_data.get('technical_details', ''),
                            'voice_path': '',  # 需要从配音数据中获取
                            'voice_duration': 0.0,  # 需要从配音数据中获取
                            'image_path': '',  # 需要从图像数据中获取
                            'video_path': '',
                            'status': '未生成'
                        }
                        self.current_scenes.append(scene_data)

                    # 加载完分镜数据后，尝试匹配配音和图像数据
                    self._match_voice_and_image_data(project_data)

                    return len(self.current_scenes) > 0

            return False

        except Exception as e:
            logger.error(f"从分镜数据加载失败: {e}")
            return False

    def _match_voice_and_image_data(self, project_data):
        """匹配配音和图像数据到分镜数据"""
        try:
            # 获取配音数据
            voice_generation = project_data.get('voice_generation', {})
            voice_segments = voice_generation.get('voice_segments', [])

            # 获取图像数据
            image_generation = project_data.get('image_generation', {})

            # 为每个场景匹配配音和图像
            for i, scene_data in enumerate(self.current_scenes):
                # 匹配配音数据（按索引）
                if i < len(voice_segments):
                    voice_segment = voice_segments[i]
                    audio_path = voice_segment.get('audio_path', '')
                    if audio_path and os.path.exists(audio_path):
                        scene_data['voice_path'] = audio_path
                        # 获取音频时长
                        duration = voice_segment.get('duration', 0.0)
                        if duration <= 0:
                            duration = self._get_audio_duration(audio_path)
                        scene_data['voice_duration'] = duration

                # 匹配图像数据
                shot_id = scene_data.get('shot_id', '')
                sequence = scene_data.get('sequence', '')

                # 尝试多种匹配方式
                possible_keys = [shot_id, sequence]
                if shot_id and shot_id.startswith('### '):
                    possible_keys.append(shot_id[4:])  # 移除 "### " 前缀

                image_found = False
                for key in possible_keys:
                    if key and key in image_generation:
                        image_data = image_generation[key]
                        image_path = image_data.get('image_path', '')
                        if image_path and os.path.exists(image_path):
                            scene_data['image_path'] = image_path
                            scene_data['status'] = image_data.get('status', '已生成')
                            image_found = True
                            break

                # 如果没有找到，尝试从_get_scene_images获取
                if not image_found:
                    scene_images = self._get_scene_images(scene_data)
                    if scene_images:
                        # 使用第一张图像作为主图像
                        main_image = scene_images[0]
                        scene_data['image_path'] = main_image['path']
                        scene_data['status'] = '已生成'

            logger.info(f"完成配音和图像数据匹配，共处理 {len(self.current_scenes)} 个镜头")

        except Exception as e:
            logger.error(f"匹配配音和图像数据失败: {e}")

    def _create_scene_data(self, shot_id, scene_id, shot, voice_segments, images, videos):
        """创建场景数据"""
        # 处理不同的数据格式
        if isinstance(shot, dict):
            # 从shots_data或storyboard数据
            description = shot.get('enhanced_description') or shot.get('scene_description') or shot.get('description', '')
            original_text = shot.get('shot_original_text') or shot.get('original_text', '')
            shot_title = shot.get('shot_type') or shot.get('sequence') or '镜头'
        else:
            # 其他格式
            description = ''
            original_text = ''
            shot_title = '镜头'

        scene_data = {
            'scene_id': scene_id,
            'shot_id': shot_id,
            'scene_title': scene_id.replace('_', ' ').title(),
            'shot_title': shot_title,
            'description': description,
            'enhanced_description': description,
            'original_text': original_text,
            'voice_path': '',
            'voice_duration': 0.0,
            'image_path': '',
            'video_path': '',
            'status': '未生成'
        }

        # 查找对应的配音文件
        for voice_segment in voice_segments:
            # 支持多种匹配方式
            voice_shot_id = voice_segment.get('shot_id', '')
            if (voice_shot_id == shot_id or
                voice_shot_id == f"shot_{shot_id}" or
                voice_shot_id.endswith(f"_{shot_id}")):

                audio_path = voice_segment.get('audio_path', '')
                if audio_path and os.path.exists(audio_path):
                    scene_data['voice_path'] = audio_path
                    # 尝试从数据中获取时长，如果没有则检测音频文件
                    duration = voice_segment.get('duration', 0.0)
                    if duration <= 0:
                        duration = self._get_audio_duration(audio_path)
                    scene_data['voice_duration'] = duration
                break

        # 查找对应的图像文件 - 支持多种数据格式
        image_found = False

        # 方法1：从images数组查找
        for image in images:
            image_shot_id = image.get('shot_id', '')
            if (image_shot_id == shot_id or
                image_shot_id == f"shot_{shot_id}" or
                image_shot_id.endswith(f"_{shot_id}")):

                if image.get('is_main', False):
                    image_path = image.get('image_path', '')
                    if image_path and os.path.exists(image_path):
                        scene_data['image_path'] = image_path
                        image_found = True
                        break

        # 方法2：如果没找到，尝试从shot数据本身获取
        if not image_found and isinstance(shot, dict):
            image_path = shot.get('image_path') or shot.get('main_image_path', '')
            if image_path and os.path.exists(image_path):
                scene_data['image_path'] = image_path
                image_found = True

        # 查找对应的视频文件
        for video in videos:
            video_shot_id = video.get('shot_id', '')
            if (video_shot_id == shot_id or
                video_shot_id == f"shot_{shot_id}" or
                video_shot_id.endswith(f"_{shot_id}")):

                video_path = video.get('video_path', '')
                if video_path and os.path.exists(video_path):
                    scene_data['video_path'] = video_path
                    # 确保状态是字符串类型
                    status = video.get('status', '已生成')
                    scene_data['status'] = str(status) if status is not None else '已生成'
                break

        return scene_data

    def _get_audio_duration(self, audio_path):
        """获取音频文件时长"""
        try:
            # 检查文件是否存在
            if not audio_path or not os.path.exists(audio_path):
                return 0.0

            # 方法1：使用mutagen（最稳定，优先使用）
            try:
                from mutagen._file import File
                audio_file = File(audio_path)
                if audio_file is not None and hasattr(audio_file, 'info') and hasattr(audio_file.info, 'length'):
                    duration = float(audio_file.info.length)
                    logger.debug(f"mutagen获取音频时长成功: {audio_path} -> {duration:.1f}s")
                    return duration
            except Exception as e:
                logger.warning(f"mutagen获取音频时长失败: {e}")

            # 方法2：使用pydub
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(audio_path)
                duration = len(audio) / 1000.0  # 转换为秒
                logger.debug(f"pydub获取音频时长成功: {audio_path} -> {duration:.1f}s")
                return float(duration)
            except Exception as e:
                logger.warning(f"pydub获取音频时长失败: {e}")

            # 方法3：使用wave模块（仅支持wav文件）
            try:
                import wave
                if audio_path.lower().endswith('.wav'):
                    with wave.open(audio_path, 'r') as wav_file:
                        frames = wav_file.getnframes()
                        rate = wav_file.getframerate()
                        duration = frames / float(rate)
                        logger.debug(f"wave获取音频时长成功: {audio_path} -> {duration:.1f}s")
                        return duration
            except Exception as e:
                logger.warning(f"wave获取音频时长失败: {e}")

            # 如果所有方法都失败，返回默认值
            logger.warning(f"无法获取音频时长，使用默认值5秒: {audio_path}")
            return 5.0  # 默认5秒

        except Exception as e:
            logger.error(f"获取音频时长失败: {e}")
            return 5.0

    def _check_voice_duration_match(self, scene_data):
        """检查配音时长是否需要多个图像"""
        voice_duration = scene_data.get('voice_duration', 0.0)
        if voice_duration <= 0:
            return 1, []  # 没有配音，使用1个图像

        # CogVideoX-Flash支持的duration值
        supported_durations = [5.0, 5.5, 10.0]

        # 如果总时长不超过10秒，直接返回单个片段
        if voice_duration <= 10.0:
            # 选择最接近的支持duration
            best_duration = min(supported_durations, key=lambda x: abs(x - voice_duration))
            return 1, [best_duration]

        # 智能分段算法 - 使用支持的duration值
        segment_durations = []
        remaining_duration = voice_duration

        while remaining_duration > 0:
            if remaining_duration <= 10.0:
                # 最后一个片段，选择最接近的支持duration
                best_duration = min(supported_durations, key=lambda x: abs(x - remaining_duration))
                segment_durations.append(best_duration)
                break
            else:
                # 使用10秒片段
                segment_durations.append(10.0)
                remaining_duration -= 10.0

        required_images = len(segment_durations)
        return required_images, segment_durations

    def _get_scene_images(self, scene_data):
        """获取场景的所有图像 - 使用全局镜头编号直接映射"""
        shot_id = scene_data.get('shot_id', '')
        shot_number = scene_data.get('shot_number', 0)

        if not shot_id and not shot_number:
            return []

        # 从项目数据中获取该镜头的所有图像
        project_data = self.project_manager.get_project_data() if self.project_manager else {}
        scene_images = []

        # 方法1：从shot_image_mappings获取（使用全局镜头编号）
        shot_image_mappings = project_data.get('shot_image_mappings', {})

        # 使用全局镜头编号构建映射键
        shot_num = str(shot_number) if shot_number else None
        if not shot_num and shot_id:
            if shot_id.startswith('镜头'):
                shot_num = shot_id.replace('镜头', '')
            elif shot_id.startswith('shot_'):
                shot_num = shot_id.replace('shot_', '')
            else:
                shot_num = shot_id

        # 构建映射键 - 使用全局镜头编号
        if shot_num:
            mapping_key = f"scene_1_shot_{shot_num}"

            if mapping_key in shot_image_mappings:
                mapping_data = shot_image_mappings[mapping_key]

                # 获取主图像
                main_image_path = mapping_data.get('main_image_path', '') or mapping_data.get('image_path', '')
                if main_image_path and os.path.exists(main_image_path):
                    scene_images.append({
                        'path': main_image_path,
                        'is_main': True
                    })

                # 获取其他图像
                generated_images = mapping_data.get('generated_images', [])
                for img_path in generated_images:
                    if img_path and os.path.exists(img_path) and img_path != main_image_path:
                        scene_images.append({
                            'path': img_path,
                            'is_main': False
                        })

                if scene_images:
                    logger.debug(f"镜头 {shot_id} (编号{shot_num}) 从映射 {mapping_key} 找到 {len(scene_images)} 张图像")
                    return scene_images

        # 方法2：从image_generation数据中获取（备用）
        if not scene_images:
            image_generation = project_data.get('image_generation', {})

            # 尝试直接匹配shot_id
            if shot_id in image_generation:
                image_data = image_generation[shot_id]
                image_path = image_data.get('image_path', '')
                if image_path and os.path.exists(image_path):
                    scene_images.append({
                        'path': image_path,
                        'is_main': True
                    })

        # 方法3：从项目图像目录中按编号搜索（最后备用）
        if not scene_images and shot_num and self.project_manager and self.project_manager.current_project:
            project_dir = self.project_manager.current_project.get('project_dir', '')
            if project_dir:
                images_dir = os.path.join(project_dir, 'images')
                if os.path.exists(images_dir):
                    # 搜索包含镜头编号的图像文件
                    for root, _, files in os.walk(images_dir):
                        for file in files:
                            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
                                # 检查文件名是否包含镜头编号
                                file_lower = file.lower()
                                if shot_num in file_lower:
                                    image_path = os.path.join(root, file)
                                    if not any(img['path'] == image_path for img in scene_images):
                                        scene_images.append({
                                            'path': image_path,
                                            'is_main': len(scene_images) == 0  # 第一张作为主图
                                        })

        # 按主图像优先排序
        scene_images.sort(key=lambda x: not x['is_main'])

        logger.debug(f"镜头 {shot_id} 找到 {len(scene_images)} 张图像")
        return scene_images

    def update_scene_table(self):
        """更新场景表格"""
        try:
            scene_count = len(self.current_scenes)
            self.scene_table.setRowCount(scene_count)

            # 更新场景统计标签
            if hasattr(self, 'scene_count_label'):
                self.scene_count_label.setText(f"共 {scene_count} 个场景")

            for row, scene_data in enumerate(self.current_scenes):
                # 选择复选框
                checkbox = QCheckBox()
                checkbox.stateChanged.connect(self.on_scene_selection_changed)
                self.scene_table.setCellWidget(row, 0, checkbox)

                # 镜头信息 - 显示运镜方法（technical_details）
                technical_details = scene_data.get('technical_details', '')
                if technical_details:
                    # 提取镜头类型和运动信息
                    shot_info = []
                    if '镜头类型：' in technical_details:
                        shot_type = technical_details.split('镜头类型：')[1].split('，')[0]
                        shot_info.append(shot_type)
                    if '镜头运动：' in technical_details:
                        shot_movement = technical_details.split('镜头运动：')[1].split('，')[0]
                        shot_info.append(shot_movement)
                    shot_text = ' | '.join(shot_info) if shot_info else scene_data.get('shot_title', f'镜头{row+1}')
                else:
                    shot_text = scene_data.get('shot_title', scene_data.get('shot_id', f'镜头{row+1}'))

                self.scene_table.setItem(row, 1, QTableWidgetItem(shot_text))

                # 旁白栏 - 显示原文内容
                narration_text = scene_data.get('original_description', scene_data.get('narration', ''))
                if narration_text:
                    # 如果内容太长，截断显示
                    if len(narration_text) > 50:
                        display_text = narration_text[:47] + "..."
                    else:
                        display_text = narration_text
                else:
                    display_text = "暂无旁白"

                narration_item = QTableWidgetItem(display_text)
                narration_item.setToolTip(narration_text)  # 完整内容作为提示
                self.scene_table.setItem(row, 2, narration_item)

                # 配音状态和时长
                voice_widget = QWidget()
                voice_layout = QHBoxLayout(voice_widget)
                voice_layout.setContentsMargins(2, 2, 2, 2)

                voice_status = "✅" if scene_data['voice_path'] and os.path.exists(scene_data['voice_path']) else "❌"
                voice_status_label = QLabel(voice_status)
                voice_layout.addWidget(voice_status_label)

                # 显示配音时长
                voice_duration = scene_data.get('voice_duration', 0.0)
                if voice_duration > 0:
                    duration_label = QLabel(f"{voice_duration:.1f}s")
                    duration_label.setStyleSheet("color: #666; font-size: 10px;")
                    voice_layout.addWidget(duration_label)

                self.scene_table.setCellWidget(row, 3, voice_widget)

                # 图像预览（简化版本）
                image_widget = QWidget()
                image_layout = QVBoxLayout(image_widget)
                image_layout.setContentsMargins(2, 2, 2, 2)
                image_layout.setSpacing(2)

                # 获取该镜头的图像
                scene_images = self._get_scene_images(scene_data)
                image_path = scene_data.get('image_path', '')

                # 优先使用scene_data中的image_path，如果没有则使用scene_images中的第一张
                if not image_path and scene_images:
                    image_path = scene_images[0]['path']
                    scene_data['image_path'] = image_path

                # 图像预览
                if image_path and os.path.exists(image_path):
                    image_preview = QLabel()
                    try:
                        pixmap = QPixmap(image_path)
                        if not pixmap.isNull():
                            # 缩略图尺寸
                            scaled_pixmap = pixmap.scaled(160, 120, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                            image_preview.setPixmap(scaled_pixmap)
                            image_preview.setToolTip(f"图像预览: {os.path.basename(image_path)}")
                            image_layout.addWidget(image_preview)
                        else:
                            logger.warning(f"无法加载图像: {image_path}")
                    except Exception as e:
                        logger.error(f"加载图像预览失败: {e}")

                # 图像数量信息
                available_count = len(scene_images)
                voice_duration = scene_data.get('voice_duration', 0.0)
                required_images, _ = self._check_voice_duration_match(scene_data)

                if voice_duration > 10.0:
                    # 需要多张图片的情况
                    info_label = QLabel(f"需要{required_images}张，已有{available_count}张")
                    if available_count >= required_images:
                        info_label.setStyleSheet("color: #4CAF50; font-size: 10px;")
                    else:
                        info_label.setStyleSheet("color: #F44336; font-size: 10px;")
                else:
                    # 单张图片的情况
                    if available_count > 0:
                        info_label = QLabel(f"已有{available_count}张")
                        info_label.setStyleSheet("color: #4CAF50; font-size: 10px;")
                    else:
                        info_label = QLabel("无图像")
                        info_label.setStyleSheet("color: #F44336; font-size: 10px;")

                image_layout.addWidget(info_label)
                self.scene_table.setCellWidget(row, 4, image_widget)



                # AI音效栏 - 显示音效提示和启用开关
                audio_widget = QWidget()
                audio_layout = QVBoxLayout(audio_widget)
                audio_layout.setContentsMargins(2, 2, 2, 2)
                audio_layout.setSpacing(1)

                # AI音效启用复选框
                audio_checkbox = QCheckBox("启用AI音效")
                audio_checkbox.setChecked(True)  # 默认启用
                audio_checkbox.setStyleSheet("font-size: 10px; color: #333;")
                # 设置对象名称以便后续查找
                audio_checkbox.setObjectName(f"audio_checkbox_{row}")
                audio_layout.addWidget(audio_checkbox)

                # 获取音效提示
                audio_hint = self._get_audio_hint_for_scene(scene_data)
                if audio_hint:
                    hint_label = QLabel(f"音效: {audio_hint}")
                    hint_label.setWordWrap(True)  # 允许换行
                    hint_label.setStyleSheet("font-size: 9px; color: #666; line-height: 1.2;")
                    hint_label.setAlignment(Qt.AlignmentFlag.AlignTop)
                    audio_layout.addWidget(hint_label)
                else:
                    no_hint_label = QLabel("暂无音效提示")
                    no_hint_label.setStyleSheet("font-size: 9px; color: #ccc;")
                    audio_layout.addWidget(no_hint_label)

                self.scene_table.setCellWidget(row, 5, audio_widget)

                # 视频状态和预览
                video_widget = QWidget()
                video_layout = QHBoxLayout(video_widget)
                video_layout.setContentsMargins(2, 2, 2, 2)

                video_status = "✅" if scene_data['video_path'] and os.path.exists(scene_data['video_path']) else "❌"
                video_status_label = QLabel(video_status)
                video_layout.addWidget(video_status_label)

                # 如果有视频，添加播放按钮
                if scene_data['video_path'] and os.path.exists(scene_data['video_path']):
                    play_btn = QPushButton("▶")
                    play_btn.setMaximumSize(30, 25)
                    play_btn.setToolTip("播放视频")
                    play_btn.clicked.connect(lambda checked=False, path=scene_data['video_path']: self.play_video(path))
                    video_layout.addWidget(play_btn)

                self.scene_table.setCellWidget(row, 6, video_widget)  # 视频栏移到第6列

                # 状态按钮
                action_widget = QWidget()
                action_layout = QVBoxLayout(action_widget)
                action_layout.setContentsMargins(2, 2, 2, 2)
                action_layout.setSpacing(2)

                # 检查配音时长和所需图像数量
                voice_duration = scene_data.get('voice_duration', 0.0)
                required_images, segment_durations = self._check_voice_duration_match(scene_data)
                scene_images = self._get_scene_images(scene_data)

                # 生成视频按钮
                generate_btn = QPushButton("🎬 生成")
                generate_btn.setMaximumSize(80, 25)

                # 检查视频文件是否存在来确定真实状态
                video_path = scene_data.get('video_path', '')
                has_video = video_path and os.path.exists(video_path)

                # 根据实际状态设置按钮样式和文本
                if has_video:
                    generate_btn.setText("✅ 已生成")
                    generate_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-size: 10px; }")
                    generate_btn.setEnabled(True)  # 允许重新生成
                elif scene_data.get('status', '') == '生成中':
                    generate_btn.setText("⏸ 生成中...")
                    generate_btn.setEnabled(False)
                    generate_btn.setStyleSheet("QPushButton { background-color: #FFC107; color: black; font-size: 10px; }")
                else:
                    generate_btn.setText("🎬 生成")
                    generate_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-size: 10px; }")

                # 检查是否有足够的图像文件
                has_enough_images = len(scene_images) >= required_images

                # 设置按钮状态和提示
                if voice_duration > 10.0 and not has_enough_images:
                    generate_btn.setEnabled(False)
                    generate_btn.setToolTip(f"配音时长{voice_duration:.1f}s，需要{required_images}个图像，当前只有{len(scene_images)}个")
                    generate_btn.setStyleSheet("QPushButton { background-color: #F44336; color: white; font-size: 10px; }")
                elif voice_duration > 10.0:
                    generate_btn.setEnabled(True)
                    generate_btn.setToolTip(f"配音时长{voice_duration:.1f}s，将生成{required_images}个视频片段")
                else:
                    has_image = scene_data['image_path'] and os.path.exists(scene_data['image_path'])
                    has_voice = scene_data['voice_path'] and os.path.exists(scene_data['voice_path'])
                    is_not_generating = scene_data.get('status', '未生成') != '生成中'

                    # 确保所有值都是布尔类型
                    enable_button = bool(has_image and has_voice and is_not_generating)
                    generate_btn.setEnabled(enable_button)

                    if voice_duration > 0:
                        generate_btn.setToolTip(f"配音时长{voice_duration:.1f}s，生成单个视频")
                    else:
                        generate_btn.setToolTip("生成视频")

                generate_btn.clicked.connect(lambda checked=False, r=row: self.generate_single_video(r))
                action_layout.addWidget(generate_btn)

                # 显示视频片段信息（仅当需要多片段时）
                if voice_duration > 10.0:
                    segment_info = QLabel(f"{required_images}片段")
                    segment_info.setStyleSheet("color: #666; font-size: 9px;")
                    segment_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    segment_info.setToolTip(f"配音时长{voice_duration:.1f}s，需要生成{required_images}个视频片段")
                    action_layout.addWidget(segment_info)

                self.scene_table.setCellWidget(row, 7, action_widget)  # 状态栏移到第7列

            # 连接行选择事件
            self.scene_table.itemSelectionChanged.connect(self.on_scene_row_selected)

        except Exception as e:
            logger.error(f"更新场景表格失败: {e}")

    def play_video(self, video_path):
        """播放视频"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                subprocess.run(["start", video_path], shell=True, check=True)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", video_path], check=True)
            else:  # Linux
                subprocess.run(["xdg-open", video_path], check=True)

        except Exception as e:
            logger.error(f"播放视频失败: {e}")
            QMessageBox.warning(self, "警告", f"无法播放视频: {str(e)}")

    def generate_single_video(self, row):
        """生成单个视频"""
        try:
            if row < 0 or row >= len(self.current_scenes):
                return

            scene_data = self.current_scenes[row]

            # 检查必要文件
            if not scene_data['image_path'] or not os.path.exists(scene_data['image_path']):
                QMessageBox.warning(self, "警告", "该场景缺少图像文件")
                return

            # 开始生成
            self.start_generation([scene_data])

        except Exception as e:
            logger.error(f"生成单个视频失败: {e}")
            QMessageBox.critical(self, "错误", f"生成失败: {str(e)}")

    def on_scene_selection_changed(self):
        """场景选择状态改变"""
        selected_count = self.get_selected_scene_count()
        self.batch_generate_btn.setText(f"🎬 批量生成 ({selected_count})")
        self.batch_generate_btn.setEnabled(selected_count > 0)

        # 更新动态时长显示
        self._update_dynamic_duration_display()

    def on_scene_row_selected(self):
        """场景行被选中"""
        try:
            current_row = self.scene_table.currentRow()
            if 0 <= current_row < len(self.current_scenes):
                scene_data = self.current_scenes[current_row]

                # 更新图像预览 - 支持多张图片显示
                self.update_image_preview_with_multiple(scene_data)

                # 更新描述预览 - 显示 original_description + technical_details
                original_description = scene_data.get('original_description', '')
                technical_details = scene_data.get('technical_details', '')
                enhanced_description = scene_data.get('enhanced_description', '')

                preview_text = ""
                if original_description:
                    preview_text += f"旁白内容：\n{original_description}\n\n"
                if enhanced_description:
                    preview_text += f"增强描述：\n{enhanced_description}\n\n"
                if technical_details:
                    preview_text += f"技术细节：\n{technical_details}"

                # 场景描述已删除，不再需要设置预览文本

                # 启用单个生成按钮
                image_path = scene_data.get('image_path', '')
                has_image = bool(image_path and os.path.exists(image_path))
                self.single_generate_btn.setEnabled(has_image)

                # 更新动态时长显示
                self._update_single_scene_duration_display(scene_data)

        except Exception as e:
            logger.error(f"处理场景选择失败: {e}")

    def _update_dynamic_duration_display(self):
        """更新动态时长显示"""
        try:
            selected_scenes = self.get_selected_scenes()

            if not selected_scenes:
                self.dynamic_duration_label.setText("选择镜头查看配音时长")
                self.multi_segment_label.setVisible(False)
                return

            total_duration = 0.0
            multi_segment_count = 0

            for scene in selected_scenes:
                voice_duration = scene.get('voice_duration', 0.0)
                total_duration += voice_duration

                if voice_duration > 10.0:
                    multi_segment_count += 1

            # 显示总时长
            if len(selected_scenes) == 1:
                scene = selected_scenes[0]
                voice_duration = scene.get('voice_duration', 0.0)
                if voice_duration > 0:
                    self.dynamic_duration_label.setText(f"配音时长: {voice_duration:.1f}秒")
                else:
                    self.dynamic_duration_label.setText("该镜头无配音")
            else:
                self.dynamic_duration_label.setText(f"总配音时长: {total_duration:.1f}秒 ({len(selected_scenes)}个镜头)")

            # 显示多片段提示
            if multi_segment_count > 0:
                self.multi_segment_label.setText(f"⚠️ {multi_segment_count}个镜头需要多片段生成（配音>10秒）")
                self.multi_segment_label.setVisible(True)
            else:
                self.multi_segment_label.setVisible(False)

        except Exception as e:
            logger.error(f"更新动态时长显示失败: {e}")

    def _update_single_scene_duration_display(self, scene_data):
        """更新单个场景的时长显示"""
        try:
            voice_duration = scene_data.get('voice_duration', 0.0)

            if voice_duration > 0:
                self.dynamic_duration_label.setText(f"配音时长: {voice_duration:.1f}秒")

                if voice_duration > 10.0:
                    required_images, segment_durations = self._check_voice_duration_match(scene_data)
                    self.multi_segment_label.setText(f"⚠️ 需要{required_images}个片段: {', '.join([f'{d:.1f}s' for d in segment_durations])}")
                    self.multi_segment_label.setVisible(True)
                else:
                    self.multi_segment_label.setVisible(False)
            else:
                self.dynamic_duration_label.setText("该镜头无配音")
                self.multi_segment_label.setVisible(False)

        except Exception as e:
            logger.error(f"更新单个场景时长显示失败: {e}")

    def update_image_preview_with_multiple(self, scene_data):
        """更新图像预览，支持多张图片显示"""
        try:
            # 获取该镜头的所有图像
            scene_images = self._get_scene_images(scene_data)
            main_image_path = scene_data.get('image_path', '')

            # 存储当前场景的图像数据
            self._current_scene_images = scene_images
            self._current_image_index = 0

            if scene_images:
                # 如果有主图像路径，找到它在列表中的位置
                if main_image_path:
                    for i, img in enumerate(scene_images):
                        if img['path'] == main_image_path:
                            self._current_image_index = i
                            break

                # 显示当前图像
                self._display_current_image()

                # 显示/隐藏切换按钮
                if len(scene_images) > 1:
                    self.prev_image_btn.setVisible(True)
                    self.next_image_btn.setVisible(True)
                    self.image_info_label.setVisible(True)
                else:
                    self.prev_image_btn.setVisible(False)
                    self.next_image_btn.setVisible(False)
                    self.image_info_label.setText("1/1")
                    self.image_info_label.setVisible(True)
            else:
                # 没有图像
                self.image_preview.setText("无图像文件")
                self.prev_image_btn.setVisible(False)
                self.next_image_btn.setVisible(False)
                self.image_info_label.setVisible(False)

        except Exception as e:
            logger.error(f"更新图像预览失败: {e}")
            self.image_preview.setText("图像加载失败")
            self.prev_image_btn.setVisible(False)
            self.next_image_btn.setVisible(False)
            self.image_info_label.setVisible(False)

    def update_image_preview(self, image_path):
        """更新图像预览（兼容方法）"""
        try:
            if image_path and os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 缩放图像以适应预览区域
                    scaled_pixmap = pixmap.scaled(
                        self.image_preview.size(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.image_preview.setPixmap(scaled_pixmap)
                    self.image_preview.setToolTip("图像预览")
                else:
                    self.image_preview.setText("无法加载图像")
            else:
                self.image_preview.setText("无图像文件")

        except Exception as e:
            logger.error(f"更新图像预览失败: {e}")
            self.image_preview.setText("图像加载失败")

    def show_previous_image(self):
        """显示上一张图片"""
        try:
            if hasattr(self, '_current_scene_images') and hasattr(self, '_current_image_index'):
                if self._current_image_index > 0:
                    self._current_image_index -= 1
                    self._display_current_image()
        except Exception as e:
            logger.error(f"显示上一张图片失败: {e}")

    def show_next_image(self):
        """显示下一张图片"""
        try:
            if hasattr(self, '_current_scene_images') and hasattr(self, '_current_image_index'):
                if self._current_image_index < len(self._current_scene_images) - 1:
                    self._current_image_index += 1
                    self._display_current_image()
        except Exception as e:
            logger.error(f"显示下一张图片失败: {e}")

    def _display_current_image(self):
        """显示当前索引的图片"""
        try:
            if (hasattr(self, '_current_scene_images') and
                hasattr(self, '_current_image_index') and
                0 <= self._current_image_index < len(self._current_scene_images)):

                current_image = self._current_scene_images[self._current_image_index]
                image_path = current_image['path']

                if os.path.exists(image_path):
                    pixmap = QPixmap(image_path)
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(
                            self.image_preview.size(),
                            Qt.AspectRatioMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation
                        )
                        self.image_preview.setPixmap(scaled_pixmap)

                        # 更新控制按钮状态
                        self.prev_image_btn.setEnabled(self._current_image_index > 0)
                        self.next_image_btn.setEnabled(self._current_image_index < len(self._current_scene_images) - 1)

                        # 更新信息标签
                        total_count = len(self._current_scene_images)
                        current_num = self._current_image_index + 1
                        main_text = " (主图)" if current_image.get('is_main', False) else ""
                        self.image_info_label.setText(f"{current_num}/{total_count}{main_text}")
                    else:
                        self.image_preview.setText("无法加载图像")
                else:
                    self.image_preview.setText("图像文件不存在")

        except Exception as e:
            logger.error(f"显示当前图片失败: {e}")
            self.image_preview.setText("图像显示失败")

    def get_selected_scene_count(self):
        """获取选中的场景数量"""
        count = 0
        for row in range(self.scene_table.rowCount()):
            checkbox = self.scene_table.cellWidget(row, 0)
            if checkbox and isinstance(checkbox, QCheckBox) and checkbox.isChecked():
                count += 1
        return count

    def get_selected_scenes(self):
        """获取选中的场景数据"""
        selected_scenes = []
        for row in range(self.scene_table.rowCount()):
            checkbox = self.scene_table.cellWidget(row, 0)
            if checkbox and isinstance(checkbox, QCheckBox) and checkbox.isChecked() and row < len(self.current_scenes):
                selected_scenes.append(self.current_scenes[row])
        return selected_scenes

    def select_all_scenes(self):
        """全选场景"""
        for row in range(self.scene_table.rowCount()):
            checkbox = self.scene_table.cellWidget(row, 0)
            if checkbox and isinstance(checkbox, QCheckBox):
                checkbox.setChecked(True)

    def select_none_scenes(self):
        """取消全选场景"""
        for row in range(self.scene_table.rowCount()):
            checkbox = self.scene_table.cellWidget(row, 0)
            if checkbox and isinstance(checkbox, QCheckBox):
                checkbox.setChecked(False)

    def start_single_generation(self):
        """开始单个视频生成"""
        try:
            current_row = self.scene_table.currentRow()
            if current_row < 0 or current_row >= len(self.current_scenes):
                QMessageBox.warning(self, "警告", "请先选择一个场景")
                return

            scene_data = self.current_scenes[current_row]

            # 检查必要文件
            if not scene_data['image_path'] or not os.path.exists(scene_data['image_path']):
                QMessageBox.warning(self, "警告", "该场景缺少图像文件")
                return

            # 开始生成
            self.start_generation([scene_data])

        except Exception as e:
            logger.error(f"开始单个生成失败: {e}")
            QMessageBox.critical(self, "错误", f"开始生成失败: {str(e)}")

    def start_batch_generation(self):
        """开始批量视频生成"""
        try:
            selected_scenes = self.get_selected_scenes()

            if not selected_scenes:
                QMessageBox.warning(self, "警告", "请先选择要生成的场景")
                return

            # 检查选中场景的图像文件
            missing_images = []
            for scene in selected_scenes:
                if not scene['image_path'] or not os.path.exists(scene['image_path']):
                    missing_images.append(f"{scene['scene_title']}-{scene['shot_title']}")

            if missing_images:
                reply = QMessageBox.question(
                    self, "确认",
                    f"以下场景缺少图像文件，是否跳过？\n\n{chr(10).join(missing_images)}",
                    QMessageBox.StandardButton.Yes,
                    QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return

                # 过滤掉缺少图像的场景
                selected_scenes = [s for s in selected_scenes if s['image_path'] and os.path.exists(s['image_path'])]

            if not selected_scenes:
                QMessageBox.warning(self, "警告", "没有可生成的场景")
                return

            # 开始生成
            self.start_generation(selected_scenes)

        except Exception as e:
            logger.error(f"开始批量生成失败: {e}")
            QMessageBox.critical(self, "错误", f"开始生成失败: {str(e)}")

    def start_generation(self, scenes_to_generate):
        """开始视频生成"""
        try:
            if self.current_worker and self.current_worker.isRunning():
                QMessageBox.warning(self, "警告", "已有生成任务在进行中")
                return

            # 准备生成配置
            generation_config = self.get_generation_config()

            # 设置生成队列
            self.generation_queue = scenes_to_generate.copy()

            # 开始第一个生成任务
            self.process_next_generation()

        except Exception as e:
            logger.error(f"开始生成失败: {e}")
            QMessageBox.critical(self, "错误", f"开始生成失败: {str(e)}")

    def process_next_generation(self):
        """处理下一个生成任务"""
        try:
            if not self.generation_queue:
                # 所有任务完成
                self.on_all_generation_complete()
                return

            # 获取下一个场景
            current_scene = self.generation_queue.pop(0)

            # 保存当前生成的场景
            self._current_generating_scene = current_scene

            # 更新状态
            self.update_scene_status(current_scene, '生成中')

            # 检查是否需要多片段生成
            voice_duration = current_scene.get('voice_duration', 0.0)
            required_images, segment_durations = self._check_voice_duration_match(current_scene)
            scene_images = self._get_scene_images(current_scene)

            # 获取生成配置
            image_path = current_scene.get('image_path', '')

            if voice_duration > 10.0 and len(scene_images) >= required_images:
                # 多片段生成模式
                self._generate_multi_segment_video(current_scene, scene_images, segment_durations)
            else:
                # 单片段生成模式
                generation_config = self.get_generation_config(image_path, voice_duration if voice_duration > 0 else None)

            # 创建工作线程
            self.current_worker = VideoGenerationWorker(
                current_scene,
                generation_config,
                self.project_manager,
                self.project_manager.current_project_name if self.project_manager else None
            )

            # 连接信号
            self.current_worker.progress_updated.connect(self.on_progress_updated)
            self.current_worker.video_generated.connect(self.on_video_generated)

            # 显示进度界面
            self.show_generation_progress()

            # 开始生成
            self.current_worker.start()

        except Exception as e:
            logger.error(f"处理下一个生成任务失败: {e}")
            self.on_generation_error(str(e))

    def get_generation_config(self, image_path=None, target_duration=None):
        """获取生成配置"""
        try:
            # CogVideoX-Flash支持的分辨率
            supported_resolutions = [
                (720, 480), (1024, 1024), (1280, 960),
                (960, 1280), (1920, 1080), (1080, 1920),
                (2048, 1080), (3840, 2160)
            ]

            # 默认分辨率
            width, height = 1024, 1024

            # 如果提供了图像路径，尝试获取图像尺寸并调整为支持的分辨率
            if image_path and os.path.exists(image_path):
                try:
                    from PIL import Image
                    with Image.open(image_path) as img:
                        img_width, img_height = img.size
                        logger.info(f"原始图像尺寸: {img_width}x{img_height}")

                        # 🔧 找到最接近的支持分辨率
                        best_resolution = self._find_best_resolution(img_width, img_height, supported_resolutions)
                        width, height = best_resolution
                        logger.info(f"调整为支持的分辨率: {width}x{height}")
                except Exception as e:
                    logger.warning(f"无法获取图像尺寸，使用默认值: {e}")

            # 如果没有图像路径，使用UI设置的分辨率
            if not image_path:
                resolution_text = self.resolution_combo.currentText()
                if 'x' in resolution_text:
                    try:
                        ui_width, ui_height = resolution_text.split('x')[0], resolution_text.split('x')[1].split(' ')[0]
                        ui_width, ui_height = int(ui_width), int(ui_height)
                        # 确保UI选择的分辨率也是支持的
                        if (ui_width, ui_height) in supported_resolutions:
                            width, height = ui_width, ui_height
                        else:
                            # 找到最接近的支持分辨率
                            width, height = self._find_best_resolution(ui_width, ui_height, supported_resolutions)
                            logger.info(f"UI分辨率{ui_width}x{ui_height}不支持，调整为: {width}x{height}")
                    except:
                        width, height = 1024, 1024

            # 确定视频时长
            if target_duration is not None:
                # 使用指定的目标时长
                duration = min(target_duration, 10.0)  # CogVideoX-Flash最大10秒
            else:
                # 默认使用5秒时长（当没有配音时）
                duration = 5.0

            # 🔧 确保FPS值是CogVideoX-Flash支持的
            supported_fps = [30, 60]  # 更新为实际支持的FPS
            ui_fps = int(self.fps_combo.currentText())
            if ui_fps not in supported_fps:
                # 选择最接近的支持FPS
                fps = min(supported_fps, key=lambda x: abs(x - ui_fps))
                logger.info(f"FPS {ui_fps} 不支持，调整为: {fps}")
            else:
                fps = ui_fps

            return {
                'engine': 'cogvideox_flash',
                'duration': duration,
                'fps': fps,
                'width': width,
                'height': height,
                'motion_intensity': self.motion_slider.value() / 100.0
            }

        except Exception as e:
            logger.error(f"获取生成配置失败: {e}")
            return {
                'engine': 'cogvideox_flash',
                'duration': 5.0,
                'fps': 24,
                'width': 1024,
                'height': 1024,
                'motion_intensity': 0.5
            }

    def _find_best_resolution(self, target_width, target_height, supported_resolutions):
        """找到最接近目标尺寸的支持分辨率"""
        try:
            target_ratio = target_width / target_height
            best_resolution = (1024, 1024)
            min_diff = float('inf')

            for width, height in supported_resolutions:
                # 计算比例差异
                ratio = width / height
                ratio_diff = abs(ratio - target_ratio)

                # 计算尺寸差异
                size_diff = abs(width - target_width) + abs(height - target_height)

                # 综合评分（比例权重更高）
                total_diff = ratio_diff * 1000 + size_diff

                if total_diff < min_diff:
                    min_diff = total_diff
                    best_resolution = (width, height)

            return best_resolution

        except Exception as e:
            logger.error(f"查找最佳分辨率失败: {e}")
            return (1024, 1024)

    def _generate_multi_segment_video(self, scene_data, scene_images, segment_durations):
        """生成多片段视频"""
        try:
            # 创建多片段生成工作线程
            self.current_worker = MultiSegmentVideoWorker(
                scene_data,
                scene_images,
                segment_durations,
                self.project_manager,
                self.project_manager.current_project_name if self.project_manager else None
            )

            # 连接信号
            self.current_worker.progress_updated.connect(self.on_progress_updated)
            self.current_worker.video_generated.connect(self.on_video_generated)

            # 显示进度界面
            self.show_generation_progress()

            # 开始生成
            self.current_worker.start()

        except Exception as e:
            logger.error(f"多片段视频生成失败: {e}")
            self.on_generation_error(str(e))

    def show_generation_progress(self):
        """显示生成进度"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.cancel_btn.setVisible(True)
        self.batch_generate_btn.setEnabled(False)
        self.single_generate_btn.setEnabled(False)

    def hide_generation_progress(self):
        """隐藏生成进度"""
        self.progress_bar.setVisible(False)
        self.cancel_btn.setVisible(False)
        self.batch_generate_btn.setEnabled(True)
        self.single_generate_btn.setEnabled(True)

    def update_scene_status(self, scene_data, status):
        """更新场景状态"""
        try:
            # 在场景列表中找到对应场景并更新状态
            for i, scene in enumerate(self.current_scenes):
                # 使用多种方式匹配场景
                scene_match = False

                # 方式1：通过scene_id和shot_id匹配
                if (scene.get('scene_id') == scene_data.get('scene_id') and
                    scene.get('shot_id') == scene_data.get('shot_id')):
                    scene_match = True

                # 方式2：通过scene_index和shot_index匹配（兼容旧格式）
                elif (scene.get('scene_index') == scene_data.get('scene_index') and
                      scene.get('shot_index') == scene_data.get('shot_index')):
                    scene_match = True

                # 方式3：通过索引匹配
                elif i < len(self.current_scenes) and scene == scene_data:
                    scene_match = True

                if scene_match:
                    scene['status'] = status
                    # 刷新表格显示
                    self.update_scene_table()
                    break

        except Exception as e:
            logger.error(f"更新场景状态失败: {e}")

    def on_progress_updated(self, progress, message):
        """进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_video_generated(self, video_path, success, error_message):
        """视频生成完成"""
        try:
            if success:
                # 保存视频路径到项目数据
                self.save_video_to_project(video_path)

                # 更新当前场景状态
                if hasattr(self, '_current_generating_scene'):
                    self.update_scene_status(self._current_generating_scene, '已生成')

                # 自动播放视频
                if self.auto_play_check.isChecked():
                    self.play_video(video_path)

                self.status_label.setText(f"视频生成成功: {os.path.basename(video_path)}")

                # 刷新界面显示
                self.refresh_scene_display()

            else:
                # 更新失败状态
                if hasattr(self, '_current_generating_scene'):
                    self.update_scene_status(self._current_generating_scene, '失败')

                self.status_label.setText(f"视频生成失败: {error_message}")
                QMessageBox.critical(self, "生成失败", f"视频生成失败:\n{error_message}")

            # 处理下一个任务
            QTimer.singleShot(1000, self.process_next_generation)

        except Exception as e:
            logger.error(f"处理视频生成结果失败: {e}")
            self.on_generation_error(str(e))

    def on_all_generation_complete(self):
        """所有生成任务完成"""
        self.hide_generation_progress()
        self.status_label.setText("所有视频生成完成")
        QMessageBox.information(self, "完成", "所有视频生成任务已完成！")

    def on_generation_error(self, error_message):
        """生成错误处理"""
        self.hide_generation_progress()
        self.status_label.setText(f"生成错误: {error_message}")
        logger.error(f"视频生成错误: {error_message}")

    def cancel_generation(self):
        """取消生成"""
        try:
            if self.current_worker and self.current_worker.isRunning():
                self.current_worker.cancel()
                self.current_worker.quit()
                self.current_worker.wait(3000)  # 等待3秒

            self.generation_queue.clear()
            self.hide_generation_progress()
            self.status_label.setText("生成已取消")

        except Exception as e:
            logger.error(f"取消生成失败: {e}")

    def save_video_to_project(self, video_path):
        """保存视频路径到项目"""
        try:
            if not self.project_manager:
                return

            # 这里需要根据当前生成的场景信息保存视频路径
            # 简化处理，实际需要更详细的实现
            logger.info(f"视频已保存: {video_path}")

        except Exception as e:
            logger.error(f"保存视频到项目失败: {e}")

    def play_video(self, video_path):
        """播放视频"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                os.startfile(video_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", video_path])
            else:  # Linux
                subprocess.run(["xdg-open", video_path])

        except Exception as e:
            logger.error(f"播放视频失败: {e}")



    def _get_technical_details(self, scene_data):
        """获取镜头的technical_details信息"""
        try:
            # 从scene_data中获取镜头标识
            shot_id = scene_data.get('shot_id', '')
            shot_number = scene_data.get('shot_number', 0)

            # 构建镜头名称，使用全局镜头编号
            if shot_number:
                shot_name = f"### 镜头{shot_number}"
            elif shot_id and shot_id.startswith('镜头'):
                shot_name = f"### {shot_id}"
            else:
                return "暂无运镜数据"

            # 从project_data中查找对应的technical_details
            if hasattr(self, 'project_manager') and self.project_manager:
                project_data = self.project_manager.get_project_data()
                if project_data:
                    # 从enhanced_descriptions中查找
                    enhanced_descriptions = project_data.get('enhanced_descriptions', {})
                    if shot_name in enhanced_descriptions:
                        technical_details = enhanced_descriptions[shot_name].get('technical_details', '')
                        if technical_details:
                            return technical_details

            return "暂无运镜数据"

        except Exception as e:
            logger.error(f"获取technical_details失败: {e}")
            return "暂无运镜数据"

    def _get_audio_hint_for_scene(self, scene_data):
        """获取场景的音效提示"""
        try:
            # 从enhancement_progress.json中获取音效提示
            if hasattr(self, 'project_manager') and self.project_manager:
                project_data = self.project_manager.get_project_data()
                if project_data and 'enhancement_progress' in project_data:
                    enhancement_data = project_data['enhancement_progress']

                    # 查找对应的镜头数据
                    shot_id = scene_data.get('shot_id', '')
                    shot_number = scene_data.get('shot_number', '')

                    # 构建镜头名称
                    shot_name = ""
                    if shot_number:
                        shot_name = f"### 镜头{shot_number}"
                    elif shot_id and shot_id.startswith('镜头'):
                        shot_name = f"### {shot_id}"

                    if shot_name:
                        # 在enhanced_details中查找音效提示
                        enhanced_details = enhancement_data.get('enhanced_details', [])
                        for detail in enhanced_details:
                            if isinstance(detail, dict) and 'shot_info' in detail:
                                shot_info = detail['shot_info']
                                if shot_info.get('镜头编号') == shot_name:
                                    return shot_info.get('音效提示', '')

            return ""

        except Exception as e:
            logger.error(f"获取音效提示失败: {e}")
            return ""

    def _is_audio_enabled_for_scene(self, scene_data):
        """检查场景是否启用了AI音效"""
        try:
            # 查找当前选中的行
            current_row = self.scene_table.currentRow()
            if current_row >= 0:
                # 查找对应的音效复选框
                audio_widget = self.scene_table.cellWidget(current_row, 5)
                if audio_widget:
                    # 查找复选框
                    audio_checkbox = audio_widget.findChild(QCheckBox)
                    if audio_checkbox:
                        return audio_checkbox.isChecked()

            # 默认启用
            return True

        except Exception as e:
            logger.error(f"检查音效启用状态失败: {e}")
            return True

    def refresh_scene_display(self):
        """刷新场景显示"""
        try:
            # 重新加载项目数据以更新视频状态
            self.load_project_data()
        except Exception as e:
            logger.error(f"刷新场景显示失败: {e}")

    def _parse_technical_details(self, technical_details):
        """解析technical_details字符串为字典"""
        try:
            details_dict = {}
            if not technical_details:
                return details_dict

            # 按逗号分割
            parts = technical_details.split('，')
            for part in parts:
                if '：' in part:
                    key, value = part.split('：', 1)
                    details_dict[key.strip()] = value.strip()

            return details_dict

        except Exception as e:
            logger.error(f"解析technical_details失败: {e}")
            return {}


class MultiSegmentVideoWorker(QThread):
    """多片段视频生成工作线程"""
    progress_updated = pyqtSignal(int, str)
    video_generated = pyqtSignal(bool, str, str)

    def __init__(self, scene_data, scene_images, segment_durations, project_manager, project_name):
        super().__init__()
        self.scene_data = scene_data
        self.scene_images = scene_images
        self.segment_durations = segment_durations
        self.project_manager = project_manager
        self.project_name = project_name

    def run(self):
        """运行多片段视频生成"""
        try:
            self.progress_updated.emit(0, "开始生成多片段视频...")

            generated_videos = []
            total_segments = len(self.segment_durations)

            for i, duration in enumerate(self.segment_durations):
                if i >= len(self.scene_images):
                    break

                self.progress_updated.emit(
                    int((i / total_segments) * 80),
                    f"生成第{i+1}/{total_segments}个片段..."
                )

                # 获取当前片段的图像
                image_path = self.scene_images[i]['path']

                # 创建视频生成配置
                from src.models.video_engines.video_engine_base import VideoGenerationConfig
                # 🔧 优先使用video_prompt字段（来自prompt.json的content）
                prompt = self.scene_data.get('video_prompt',
                         self.scene_data.get('enhanced_description', ''))
                config = VideoGenerationConfig(
                    input_prompt=prompt,
                    input_image_path=image_path,
                    duration=duration,
                    fps=24,
                    motion_intensity=0.5,
                    output_dir=os.path.join(self.project_name, "videos") if self.project_name else "videos"
                )

                # 生成单个片段
                from src.models.video_engines.engines.cogvideox_engine import CogVideoXEngine
                engine = CogVideoXEngine()

                # 这里需要同步调用，因为QThread.run是同步的
                # 我们需要创建一个事件循环来运行异步代码
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    if not loop.run_until_complete(engine.initialize()):
                        raise Exception("视频引擎初始化失败")

                    result = loop.run_until_complete(engine.generate_video(config))

                    if result.success:
                        generated_videos.append(result.video_path)
                        logger.info(f"片段{i+1}生成成功: {result.video_path}")
                    else:
                        raise Exception(f"片段{i+1}生成失败: {result.error_message}")
                finally:
                    loop.close()

            # 合并视频片段
            self.progress_updated.emit(85, "合并视频片段...")
            final_video_path = self._merge_video_segments(generated_videos)

            self.progress_updated.emit(100, "视频生成完成")
            self.video_generated.emit(True, "多片段视频生成成功", final_video_path)

        except Exception as e:
            logger.error(f"多片段视频生成失败: {e}")
            self.video_generated.emit(False, str(e), "")

    def _merge_video_segments(self, video_paths):
        """合并视频片段"""
        try:
            if len(video_paths) == 1:
                return video_paths[0]

            # 使用ffmpeg合并视频
            import subprocess
            import tempfile

            # 创建输出文件路径
            output_dir = os.path.dirname(video_paths[0])
            shot_id = self.scene_data.get('shot_id', 'unknown')
            output_path = os.path.join(output_dir, f"{shot_id}_merged.mp4")

            # 创建文件列表
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                for video_path in video_paths:
                    f.write(f"file '{video_path}'\n")
                list_file = f.name

            try:
                # 使用ffmpeg合并
                cmd = [
                    'ffmpeg', '-f', 'concat', '-safe', '0',
                    '-i', list_file, '-c', 'copy', output_path, '-y'
                ]
                subprocess.run(cmd, check=True, capture_output=True)

                # 删除临时文件
                os.unlink(list_file)

                # 删除原始片段文件
                for video_path in video_paths:
                    try:
                        os.unlink(video_path)
                    except:
                        pass

                return output_path

            except subprocess.CalledProcessError as e:
                logger.error(f"ffmpeg合并失败: {e}")
                # 如果合并失败，返回第一个片段
                return video_paths[0]

        except Exception as e:
            logger.error(f"合并视频片段失败: {e}")
            # 如果合并失败，返回第一个片段
            return video_paths[0] if video_paths else ""




