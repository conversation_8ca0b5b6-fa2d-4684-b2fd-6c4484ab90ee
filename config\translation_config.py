# -*- coding: utf-8 -*-
"""
翻译服务配置文件
用于控制翻译功能的启用/禁用和优先级设置
"""

# 翻译功能总开关
# 设置为 False 可以完全禁用翻译功能，直接返回原文
ENABLE_TRANSLATION = False

# 翻译服务优先级配置
# 按优先级顺序排列，程序会依次尝试这些服务
TRANSLATION_PRIORITY = [
    'baidu',    # 百度翻译 - 质量好，但需要配置和充值
    'google',   # Google翻译 - 免费但有频率限制
    'llm'       # LLM翻译 - 备用方案，需要LLM API
]

# 重试配置
RETRY_CONFIG = {
    'max_retries': 3,           # 最大重试次数
    'base_delay': 2.0,          # 基础延迟时间（秒）
    'backoff_factor': 2.0,      # 退避因子
    'timeout': 15               # 请求超时时间（秒）
}

# 错误处理配置
ERROR_HANDLING = {
    'return_original_on_failure': True,  # 翻译失败时返回原文
    'log_errors': True,                  # 是否记录错误日志
    'silent_mode': False                 # 静默模式，不显示翻译错误警告
}

# 百度翻译特定配置
BAIDU_CONFIG = {
    'enabled': True,            # 是否启用百度翻译
    'skip_on_quota_exceeded': True,  # 配额用完时跳过而不是报错
}

# Google翻译特定配置
GOOGLE_CONFIG = {
    'enabled': True,            # 是否启用Google翻译
    'user_agents': [            # 用户代理轮换列表
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ],
    'rate_limit_delay': 1.0,    # 请求间隔延迟（秒）
}

# LLM翻译特定配置
LLM_CONFIG = {
    'enabled': True,            # 是否启用LLM翻译
    'model_preference': 'auto', # 模型偏好：'auto', 'fast', 'quality'
    'max_tokens': 1000,         # 最大token数
    'temperature': 0.3          # 温度参数
}

# 缓存配置
CACHE_CONFIG = {
    'enabled': False,           # 是否启用翻译缓存
    'max_size': 1000,          # 缓存最大条目数
    'ttl': 3600                # 缓存生存时间（秒）
}

# 使用说明
USAGE_NOTES = """
翻译配置使用说明：

1. 禁用翻译功能：
   设置 ENABLE_TRANSLATION = False

2. 调整翻译服务优先级：
   修改 TRANSLATION_PRIORITY 列表的顺序

3. 处理API配额问题：
   - 百度翻译：设置 BAIDU_CONFIG['skip_on_quota_exceeded'] = True
   - Google翻译：增加 RETRY_CONFIG['base_delay'] 和 GOOGLE_CONFIG['rate_limit_delay']

4. 错误处理：
   - 设置 ERROR_HANDLING['return_original_on_failure'] = True 确保程序不会因翻译失败而中断
   - 设置 ERROR_HANDLING['silent_mode'] = True 减少错误日志输出

5. 性能优化：
   - 启用缓存：CACHE_CONFIG['enabled'] = True
   - 调整超时时间：RETRY_CONFIG['timeout']
"""
